# Cura Development Git Manager

这是一个用于管理多个Cura相关Git仓库的便捷脚本。

## 功能特性

- 🔍 **状态检查**: 一键查看所有项目的Git状态
- ⬇️ **批量拉取**: 同时拉取所有项目的最新更改
- ⬆️ **批量推送**: 同时推送所有项目的更改到远程仓库
- 💾 **批量提交**: 一次性提交所有项目的更改
- 🔄 **一键同步**: 自动执行拉取→提交→推送的完整流程
- 📥 **克隆缺失**: 自动克隆尚未存在的仓库

## 管理的仓库

- **Cura**: 主要的切片软件
- **CuraEngine**: 切片引擎
- **Uranium**: 基础框架库
- **conan-config**: Conan配置文件
- **cura-workflows**: GitHub Actions工作流

所有仓库都指向您的私有fork: `github.com/wsd07/[项目名].git`

## 使用方法

### 基本命令

```bash
# 查看所有项目状态
./git-manager.sh status

# 拉取所有项目的最新更改
./git-manager.sh pull

# 推送所有项目的更改
./git-manager.sh push

# 提交所有项目的更改
./git-manager.sh commit-all "提交消息"

# 一键同步所有项目
./git-manager.sh sync "同步消息"

# 克隆缺失的仓库
./git-manager.sh clone

# 显示帮助信息
./git-manager.sh help
```

### 常用场景

#### 1. 每日开发开始前
```bash
# 拉取所有项目的最新更改
./git-manager.sh pull
```

#### 2. 开发完成后提交所有更改
```bash
# 查看当前状态
./git-manager.sh status

# 提交所有更改
./git-manager.sh commit-all "实现新功能: Windows安装包构建优化"

# 推送到远程仓库
./git-manager.sh push
```

#### 3. 一键同步所有项目
```bash
# 自动执行: 拉取 → 提交 → 推送
./git-manager.sh sync "每日同步: $(date '+%Y-%m-%d')"
```

#### 4. 设置新的开发环境
```bash
# 克隆所有缺失的仓库
./git-manager.sh clone
```

## 输出说明

脚本使用彩色输出来区分不同类型的信息：

- 🔵 **蓝色 [INFO]**: 一般信息
- 🟢 **绿色 [SUCCESS]**: 操作成功
- 🟡 **黄色 [WARNING]**: 警告信息
- 🔴 **红色 [ERROR]**: 错误信息
- 🟣 **紫色**: 章节标题

## 状态显示示例

```
=== 所有项目状态 ===

--- Cura ---
分支: main
状态: ## main...origin/main
工作目录干净
最后提交: 009eecc822 Add simplified Windows installer build workflow

--- CuraEngine ---
分支: main
状态: ## main...origin/main [ahead 2]
未提交的更改:
 M src/main.cpp
?? new_feature.cpp
最后提交: 79700fb2e Fix unit tests after adding a feature type
```

## 安全特性

- ✅ 推送前检查是否有未提交的更改
- ✅ 只推送已提交的更改，避免意外推送
- ✅ 每个操作都有详细的状态反馈
- ✅ 错误处理和警告提示

## 自定义配置

如果需要添加新的仓库或修改配置，编辑脚本中的以下部分：

```bash
# 项目列表
PROJECTS=(
    "Cura"
    "CuraEngine" 
    "Uranium"
    "conan-config"
    "cura-workflows"
)

# 在get_remote_url函数中添加新的URL映射
get_remote_url() {
    case "$1" in
        "新项目") echo "https://github.com/wsd07/新项目.git" ;;
        # ... 其他项目
    esac
}
```

## 故障排除

### 常见问题

1. **权限错误**: 确保脚本有执行权限
   ```bash
   chmod +x git-manager.sh
   ```

2. **路径错误**: 确保工作目录路径正确
   ```bash
   # 检查脚本中的WORK_DIR变量
   WORK_DIR="/Users/<USER>/Desktop/Cura-Dev"
   ```

3. **Git认证**: 确保已配置Git凭据
   ```bash
   git config --global user.name "Your Name"
   git config --global user.email "<EMAIL>"
   ```

## 最佳实践

1. **定期同步**: 建议每天开始和结束开发时运行同步
2. **有意义的提交消息**: 使用描述性的提交消息
3. **检查状态**: 推送前先检查项目状态
4. **备份重要更改**: 重要更改前先创建分支

---

**注意**: 此脚本专为您的Cura开发环境设计，所有仓库都指向您的私有GitHub仓库 (`github.com/wsd07/`)。
