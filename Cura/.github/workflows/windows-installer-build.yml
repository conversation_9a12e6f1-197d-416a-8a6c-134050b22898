name: Windows Installer Build
run-name: Build Windows Installer ${{ inputs.cura_conan_version }} by @${{ github.actor }}

on:
  workflow_dispatch:
    inputs:
      cura_conan_version:
        description: 'Cura Conan Version (leave empty to use latest from conanfile.py)'
        default: ''
        type: string
      package_overrides:
        description: 'List of specific packages to be used (space-separated, e.g., "cura/5.8.0@ultimaker/testing")'
        default: ''
        type: string
      conan_args:
        description: 'Additional Conan arguments'
        default: ''
        required: false
        type: string
      enterprise:
        description: 'Build Cura as an Enterprise edition'
        default: false
        required: true
        type: boolean
      staging:
        description: 'Use staging API'
        default: false
        required: true
        type: boolean
      private_data:
        description: 'Build with private/internal data'
        required: false
        default: false
        type: boolean

permissions:
  contents: read

env:
  # Note: These secrets need to be configured in your repository settings
  # WIN_CERT_INSTALLER_CER: ${{ secrets.WIN_CERT_INSTALLER_CER }}
  # WIN_CERT_INSTALLER_CER_PASS: ${{ secrets.WIN_CERT_INSTALLER_CER_PASS }}
  # WIN_TOKEN_CONTAINER: ${{ secrets.WIN_TOKEN_CONTAINER }}
  SENTRY_TOKEN: ${{ secrets.CURAENGINE_SENTRY_TOKEN }}
  CONAN_USER: ${{ secrets.CONAN_USER }}
  CONAN_PASS: ${{ secrets.CONAN_PASS }}

jobs:
  windows-installer-build:
    name: Build Windows Installer
    runs-on: windows-2022

    steps:
      - name: Cleanup workspace
        shell: bash
        run: |
          set -e
          find . -mindepth 1 -delete

      - name: Checkout Cura repository
        uses: actions/checkout@v4
        with:
          path: _cura_sources

      - name: Checkout Cura-workflows repository
        uses: actions/checkout@v4
        with:
          repository: wsd07/cura-workflows  # Use your forked version
          path: Cura-workflows
          ref: main

      - name: Setup Python and pip
        uses: actions/setup-python@v5
        id: setup-python
        with:
          update-environment: false
          python-version: '3.13'

      - name: Setup build environment
        shell: bash
        run: |
          pydir="$(dirname "${{ steps.setup-python.outputs.python-path }}")"
          PATH+=":$pydir:$pydir/Scripts"
          echo $pydir >> pydir.txt
          echo "Installed Python for GitHub in: $pydir"

          python -m pip install -r Cura-workflows/.github/workflows/requirements-runner.txt

          conan profile detect -f
          conan config install https://github.com/wsd07/conan-config.git -a "-b master"  # Use your forked conan-config

          if [ "${{ env.CONAN_USER }}" != "" ] && [ "${{ env.CONAN_PASS }}" != "" ]; then
            conan remote login cura-conan2 ${{ env.CONAN_USER }} -p ${{ env.CONAN_PASS }}
          fi

          if [ "${{ inputs.private_data }}" == "true" ]; then
            conan remote enable cura-private-conan2
            if [ "${{ env.CONAN_USER }}" != "" ] && [ "${{ env.CONAN_PASS }}" != "" ]; then
              conan remote login cura-private-conan2 ${{ env.CONAN_USER }} -p ${{ env.CONAN_PASS }}
            fi
          fi

          if [[ -f _cura_sources/conanfile.py ]]; then
            conan inspect _cura_sources > package_details.txt
            package_name=$(cat package_details.txt | awk '/^name:/ {print $2}')
            package_version=$(cat package_details.txt | awk '/^version:/ {print $2}')
            echo "PACKAGE_NAME=$package_name" >> $GITHUB_ENV
            echo "PACKAGE_VERSION=$package_version" >> $GITHUB_ENV
          fi

      - name: Set package overrides
        shell: bash
        run: |
          if [[ -f pydir.txt ]]; then
            pydir=$(cat pydir.txt)
            PATH+=":$pydir:$pydir/Scripts"
          fi

          profile_path=$(conan profile path "installer.jinja")
          profile_override_suffix=.override
          profile_path_override=$profile_path$profile_override_suffix
          cp -r "$profile_path" "$profile_path_override"

          if [ "${{ inputs.package_overrides }}" != "" ]; then
            echo "[replace_requires]" >> "$profile_path_override"
            for package_version in ${{ inputs.package_overrides }}; do
              package_name=$(echo "$package_version" | cut -d'/' -f1)
              echo $package_name/*@*/*: $package_version >> "$profile_path_override"
            done
          fi

          echo "CONAN_PROFILE=installer.jinja$profile_override_suffix" >> $GITHUB_ENV

      - name: Gather/build the packages
        shell: powershell
        run: |
          $pydir = Get-Content pydir.txt
          $env:PATH += ";$pydir;$pydir/Scripts"
          
          $cura_version = if ("${{ inputs.cura_conan_version }}" -ne "") { "${{ inputs.cura_conan_version }}" } else { "cura/${{ env.PACKAGE_VERSION }}@ultimaker/testing" }
          $enterprise_flag = if ("${{ inputs.enterprise }}" -eq "true") { '-o "cura/*:enterprise=True"' } else { '' }
          $staging_flag = if ("${{ inputs.staging }}" -eq "true") { '-o "cura/*:staging=True"' } else { '' }
          $private_flag = if ("${{ inputs.private_data }}" -eq "true") { '-o "cura/*:internal=True"' } else { '' }
          
          $conan_cmd = "conan install --requires `"$cura_version`" ${{ inputs.conan_args }} --build=missing --update -of cura_inst --deployer-package=`"*`" --profile ${{ env.CONAN_PROFILE }} -c user.sentry:token=`"${{ env.SENTRY_TOKEN }}`" $enterprise_flag $staging_flag $private_flag"
          
          Write-Host "Executing: $conan_cmd"
          Invoke-Expression $conan_cmd

      - name: Create the Cura distribution with pyinstaller
        id: prepare-distribution
        shell: cmd
        run: |
          call cura_inst\conanrun.bat

          python -m venv cura_installer_venv
          call cura_installer_venv\Scripts\Activate.bat

          python -m pip install -r cura_inst\packaging\pip_requirements_core_basic.txt --no-warn-script-location
          python -m pip install -r cura_inst\packaging\pip_requirements_core_hashes.txt --no-warn-script-location
          python -m pip install -r cura_inst\packaging\pip_requirements_installer_basic.txt --no-warn-script-location

          python Cura-workflows\runner_scripts\prepare_installer.py --os ${{ runner.os }} --architecture X64 ${{ inputs.enterprise && '--enterprise' || '' }} ${{ inputs.private_data && '--internal' || '' }} --summary-output %GITHUB_STEP_SUMMARY% --variables-output %GITHUB_OUTPUT%

          pyinstaller cura_inst\UltiMaker-Cura.spec

      # Note: Code signing steps are commented out as they require specific certificates
      # Uncomment and configure these steps if you have code signing certificates
      # - name: Sign the internal executables
      #   working-directory: dist/UltiMaker-Cura
      #   run: |
      #     & signtool sign /v /fd sha256 /tr http://timestamp.sectigo.com /td sha256 /f C:\actions-runner\code_sign.cer /csp "eToken Base Cryptographic Provider" /kc ${{ secrets.WIN_TOKEN_CONTAINER }} "CuraEngine.exe"
      #     & signtool sign /v /fd sha256 /tr http://timestamp.sectigo.com /td sha256 /f C:\actions-runner\code_sign.cer /csp "eToken Base Cryptographic Provider" /kc ${{ secrets.WIN_TOKEN_CONTAINER }} "UltiMaker-Cura.exe"
      #   timeout-minutes: 2

      - name: Workaround (need exact version of msvc redistributables)
        shell: powershell
        run: |
          $MSDIR="C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Redist/MSVC"
          if ( (Get-ChildItem -Path $MSDIR -Exclude "v*").Count -ne 1 ) { 
            throw "(!!!) MULTIPLE MSVC VERSIONS IN '$MSDIR' -- NEED EXACTLY 1 TO SELECT A REDISTRIBUTABLE (!!!)" 
          }
          $MSDIR_R=(Get-ChildItem -Path $MSDIR -Exclude "v*")[0].FullName
          $MSDIR_DLLS=(Get-ChildItem -Path "$MSDIR_R/x64/Microsoft.VC*.CRT")[0].FullName
          Copy-Item "$MSDIR_DLLS/concrt140.dll" "dist/UltiMaker-Cura/."
          Copy-Item "$MSDIR_DLLS/msvcp140.dll" "dist/UltiMaker-Cura/."
          Copy-Item "$MSDIR_DLLS/msvcp140_1.dll" "dist/UltiMaker-Cura/."
          Copy-Item "$MSDIR_DLLS/msvcp140_2.dll" "dist/UltiMaker-Cura/."
          Copy-Item "$MSDIR_DLLS/vcruntime140.dll" "dist/UltiMaker-Cura/."
          Copy-Item "$MSDIR_DLLS/vcruntime140_1.dll" "dist/UltiMaker-Cura/."

      - name: Workaround (some libs linking against python3 instead of python312)
        shell: powershell
        run: |
          Copy-Item "Cura-workflows/python_dll_workaround/*" "dist/UltiMaker-Cura/."

      - name: Clean up unwanted Qt files and folders
        working-directory: dist/UltiMaker-Cura
        shell: powershell
        run: |
          Remove-Item .\* -Include "*assimp*" -Recurse -Force -ErrorAction SilentlyContinue
          Remove-Item .\* -Include "*qt6charts*" -Recurse -Force -ErrorAction SilentlyContinue
          Remove-Item .\* -Include "*qt6coap*" -Recurse -Force -ErrorAction SilentlyContinue
          Remove-Item .\* -Include "*qt6datavis*" -Recurse -Force -ErrorAction SilentlyContinue
          Remove-Item .\* -Include "*qt6labsani*" -Recurse -Force -ErrorAction SilentlyContinue
          Remove-Item .\* -Include "*qt6mqtt*" -Recurse -Force -ErrorAction SilentlyContinue
          Remove-Item .\* -Include "*qt6networkauth*" -Recurse -Force -ErrorAction SilentlyContinue
          Remove-Item .\* -Include "*quick3d*" -Recurse -Force -ErrorAction SilentlyContinue
          Remove-Item .\* -Include "*qt6timeline*" -Recurse -Force -ErrorAction SilentlyContinue
          Remove-Item .\* -Include "*qt6virtualkey*" -Recurse -Force -ErrorAction SilentlyContinue
          Remove-Item .\* -Include "*waylandcomp*" -Recurse -Force -ErrorAction SilentlyContinue
          Remove-Item .\* -Include "*qt5compat*" -Recurse -Force -ErrorAction SilentlyContinue

      - name: Create the Windows exe installer
        shell: powershell
        run: |
          $pydir = Get-Content ../pydir.txt
          $env:PATH += ";$pydir;$pydir/Scripts"
          python ..\cura_inst\packaging\NSIS\create_windows_installer.py --source_path ../cura_inst --dist_path . --filename "${{steps.prepare-distribution.outputs.INSTALLER_FILENAME}}.exe" --version "${{ steps.prepare-distribution.outputs.CURA_VERSION_FULL }}"
        working-directory: dist

      # Note: Code signing for installer is commented out
      # - name: Sign the Windows exe installer
      #   run: |
      #     & signtool sign /v /fd sha256 /tr http://timestamp.sectigo.com /td sha256 /f C:\actions-runner\code_sign.cer /csp "eToken Base Cryptographic Provider" /kc ${{ secrets.WIN_TOKEN_CONTAINER }} "${{steps.prepare-distribution.outputs.INSTALLER_FILENAME}}.exe"
      #   working-directory: dist
      #   timeout-minutes: 2

      - name: Upload the installer exe
        if: ${{ always() }}
        uses: actions/upload-artifact@v4
        with:
          name: ${{steps.prepare-distribution.outputs.INSTALLER_FILENAME}}-exe
          path: dist/${{steps.prepare-distribution.outputs.INSTALLER_FILENAME}}.exe
          retention-days: 5

      - name: Create the Windows msi installer
        shell: powershell
        run: |
          $pydir = Get-Content ../pydir.txt
          $env:PATH += ";$pydir;$pydir/Scripts"
          python ..\cura_inst\packaging\msi\create_windows_msi.py --source_path ..\cura_inst --dist_path .\UltiMaker-Cura --filename "${{ steps.prepare-distribution.outputs.INSTALLER_FILENAME }}.msi" --name "${{ steps.prepare-distribution.outputs.CURA_APP_NAME }}" --version "${{ steps.prepare-distribution.outputs.CURA_VERSION_FULL }}"
        working-directory: dist

      # Note: Code signing for MSI is commented out
      # - name: Sign the Windows msi installer
      #   run: |
      #     & signtool sign /v /fd sha256 /tr http://timestamp.sectigo.com /td sha256 /f C:\actions-runner\code_sign.cer /csp "eToken Base Cryptographic Provider" /kc ${{ secrets.WIN_TOKEN_CONTAINER }} "${{steps.prepare-distribution.outputs.INSTALLER_FILENAME}}.msi"
      #   working-directory: dist
      #   timeout-minutes: 2

      - name: Upload the installer msi
        if: ${{ always() }}
        uses: actions/upload-artifact@v4
        with:
          name: ${{steps.prepare-distribution.outputs.INSTALLER_FILENAME}}-msi
          path: dist/${{steps.prepare-distribution.outputs.INSTALLER_FILENAME}}.msi
          retention-days: 5

      - name: Upload the application exe
        if: ${{ always() }}
        uses: actions/upload-artifact@v4
        with:
          name: UltiMaker-Cura-exe
          path: dist/UltiMaker-Cura/UltiMaker-Cura.exe
          retention-days: 5

      - name: Upload the engine exe
        if: ${{ always() }}
        uses: actions/upload-artifact@v4
        with:
          name: CuraEngine-exe
          path: dist/UltiMaker-Cura/CuraEngine.exe
          retention-days: 5

      - name: Clean local cache
        if: ${{ always() }}
        shell: powershell
        run: |
          $pydir = Get-Content pydir.txt
          $env:PATH += ";$pydir;$pydir/Scripts"
          conan remove '*' --lru=1w -c
