#!/bin/bash

# Cura Development Git Manager
# 管理多个Cura相关仓库的Git操作脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 项目列表 - 根据您的实际仓库调整
PROJECTS=(
    "Cura"
    "CuraEngine" 
    "Uranium"
    "conan-config"
    "cura-workflows"
)

# 获取远程仓库URL的函数
get_remote_url() {
    case "$1" in
        "Cura") echo "https://github.com/wsd07/Cura.git" ;;
        "CuraEngine") echo "https://github.com/wsd07/CuraEngine.git" ;;
        "Uranium") echo "https://github.com/wsd07/Uranium.git" ;;
        "conan-config") echo "https://github.com/wsd07/conan-config.git" ;;
        "cura-workflows") echo "https://github.com/wsd07/cura-workflows.git" ;;
        *) echo "" ;;
    esac
}

# 工作目录
WORK_DIR="/Users/<USER>/Desktop/Cura-Dev"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "\n${PURPLE}=== $1 ===${NC}"
}

# 检查项目是否存在
check_project_exists() {
    local project=$1
    if [ -d "$WORK_DIR/$project" ]; then
        return 0
    else
        return 1
    fi
}

# 检查是否为git仓库
is_git_repo() {
    local project=$1
    cd "$WORK_DIR/$project" 2>/dev/null || return 1
    git rev-parse --git-dir > /dev/null 2>&1
    return $?
}

# 显示单个项目状态
show_project_status() {
    local project=$1
    
    if ! check_project_exists "$project"; then
        log_warning "$project: 目录不存在"
        return 1
    fi
    
    if ! is_git_repo "$project"; then
        log_warning "$project: 不是Git仓库"
        return 1
    fi
    
    cd "$WORK_DIR/$project"
    
    echo -e "\n${CYAN}--- $project ---${NC}"
    
    # 显示分支信息
    local branch=$(git branch --show-current 2>/dev/null)
    echo -e "分支: ${GREEN}$branch${NC}"
    
    # 显示远程状态
    local remote_status=$(git status -sb 2>/dev/null | head -1)
    echo "状态: $remote_status"
    
    # 显示未提交的更改
    local changes=$(git status --porcelain 2>/dev/null)
    if [ -n "$changes" ]; then
        echo -e "${YELLOW}未提交的更改:${NC}"
        git status --short
    else
        echo -e "${GREEN}工作目录干净${NC}"
    fi
    
    # 显示最后一次提交
    local last_commit=$(git log -1 --oneline 2>/dev/null)
    echo "最后提交: $last_commit"
}

# 显示所有项目状态
status_all() {
    log_header "所有项目状态"
    
    for project in "${PROJECTS[@]}"; do
        show_project_status "$project"
    done
}

# 拉取所有项目的最新更改
pull_all() {
    log_header "拉取所有项目的最新更改"
    
    for project in "${PROJECTS[@]}"; do
        if check_project_exists "$project" && is_git_repo "$project"; then
            log_info "拉取 $project..."
            cd "$WORK_DIR/$project"
            
            if git pull origin main; then
                log_success "$project 拉取成功"
            else
                log_error "$project 拉取失败"
            fi
        else
            log_warning "跳过 $project (不存在或非Git仓库)"
        fi
    done
}

# 推送所有项目的更改
push_all() {
    log_header "推送所有项目的更改"
    
    for project in "${PROJECTS[@]}"; do
        if check_project_exists "$project" && is_git_repo "$project"; then
            cd "$WORK_DIR/$project"
            
            # 检查是否有未提交的更改
            if [ -n "$(git status --porcelain)" ]; then
                log_warning "$project 有未提交的更改，跳过推送"
                continue
            fi
            
            # 检查是否有未推送的提交
            local unpushed=$(git log origin/main..HEAD --oneline 2>/dev/null)
            if [ -n "$unpushed" ]; then
                log_info "推送 $project..."
                if git push origin main; then
                    log_success "$project 推送成功"
                else
                    log_error "$project 推送失败"
                fi
            else
                log_info "$project 没有需要推送的更改"
            fi
        else
            log_warning "跳过 $project (不存在或非Git仓库)"
        fi
    done
}

# 添加并提交所有项目的更改
commit_all() {
    local commit_message="$1"
    
    if [ -z "$commit_message" ]; then
        echo "请提供提交消息"
        echo "用法: $0 commit-all \"提交消息\""
        return 1
    fi
    
    log_header "提交所有项目的更改"
    
    for project in "${PROJECTS[@]}"; do
        if check_project_exists "$project" && is_git_repo "$project"; then
            cd "$WORK_DIR/$project"
            
            # 检查是否有更改
            if [ -n "$(git status --porcelain)" ]; then
                log_info "提交 $project..."
                git add .
                if git commit -m "$commit_message"; then
                    log_success "$project 提交成功"
                else
                    log_error "$project 提交失败"
                fi
            else
                log_info "$project 没有需要提交的更改"
            fi
        else
            log_warning "跳过 $project (不存在或非Git仓库)"
        fi
    done
}

# 同步所有项目 (pull + add + commit + push)
sync_all() {
    local commit_message="$1"
    
    if [ -z "$commit_message" ]; then
        commit_message="Auto sync: $(date '+%Y-%m-%d %H:%M:%S')"
    fi
    
    log_header "同步所有项目"
    
    # 先拉取最新更改
    pull_all
    
    # 提交本地更改
    commit_all "$commit_message"
    
    # 推送更改
    push_all
    
    log_success "所有项目同步完成"
}

# 克隆缺失的仓库
clone_missing() {
    log_header "克隆缺失的仓库"
    
    cd "$WORK_DIR"
    
    for project in "${PROJECTS[@]}"; do
        if ! check_project_exists "$project"; then
            local url=$(get_remote_url "$project")
            if [ -n "$url" ]; then
                log_info "克隆 $project..."
                if git clone "$url"; then
                    log_success "$project 克隆成功"
                else
                    log_error "$project 克隆失败"
                fi
            else
                log_warning "$project 没有配置远程URL"
            fi
        else
            log_info "$project 已存在"
        fi
    done
}

# 显示帮助信息
show_help() {
    echo -e "${CYAN}Cura Development Git Manager${NC}"
    echo ""
    echo "用法: $0 [命令] [参数]"
    echo ""
    echo "命令:"
    echo "  status          显示所有项目的Git状态"
    echo "  pull            拉取所有项目的最新更改"
    echo "  push            推送所有项目的更改"
    echo "  commit-all MSG  添加并提交所有项目的更改"
    echo "  sync [MSG]      同步所有项目 (pull + commit + push)"
    echo "  clone           克隆缺失的仓库"
    echo "  help            显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 status"
    echo "  $0 commit-all \"修复构建问题\""
    echo "  $0 sync \"更新所有项目\""
    echo ""
    echo "管理的项目:"
    for project in "${PROJECTS[@]}"; do
        echo "  - $project"
    done
}

# 主函数
main() {
    # 检查工作目录
    if [ ! -d "$WORK_DIR" ]; then
        log_error "工作目录不存在: $WORK_DIR"
        exit 1
    fi
    
    case "$1" in
        "status")
            status_all
            ;;
        "pull")
            pull_all
            ;;
        "push")
            push_all
            ;;
        "commit-all")
            commit_all "$2"
            ;;
        "sync")
            sync_all "$2"
            ;;
        "clone")
            clone_missing
            ;;
        "help"|"--help"|"-h"|"")
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
